/// Exception thrown when widget to JSON or JSON to widget conversion fails
class WidgetConversionException implements Exception {
  /// The error message
  final String message;
  
  /// The widget type that caused the error
  final String? widgetType;
  
  /// The original exception that caused this error (if any)
  final dynamic originalException;
  
  /// Stack trace of the original exception
  final StackTrace? stackTrace;

  const WidgetConversionException(
    this.message, {
    this.widgetType,
    this.originalException,
    this.stackTrace,
  });

  @override
  String toString() {
    final buffer = StringBuffer('WidgetConversionException: $message');
    
    if (widgetType != null) {
      buffer.write(' (Widget type: $widgetType)');
    }
    
    if (originalException != null) {
      buffer.write('\nCaused by: $originalException');
    }
    
    return buffer.toString();
  }
}
