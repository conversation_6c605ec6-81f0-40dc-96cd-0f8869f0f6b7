import 'package:flutter/material.dart';
import '../exceptions/widget_conversion_exception.dart';
import '../core/json_serializable_widget.dart';

/// Service for introspecting widget properties and extracting configuration
///
/// This service provides methods to analyze widget instances and automatically
/// extract their properties for JSON conversion without using reflection.
class WidgetIntrospector {
  /// Extracts properties from a widget instance
  ///
  /// [widget] The widget to introspect
  /// Returns a map of property names to values
  /// Throws [WidgetConversionException] if introspection fails
  static Map<String, dynamic> extractProperties(Widget widget) {
    try {
      // If the widget implements JsonSerializableWidget, use its toJson method
      if (widget is JsonSerializableWidget) {
        final jsonWidget = widget as JsonSerializableWidget;
        return jsonWidget.toJson();
      }

      // For other widgets, extract basic properties
      final properties = <String, dynamic>{
        'widgetType': widget.runtimeType.toString().toLowerCase(),
        'runtimeType': widget.runtimeType.toString(),
      };

      // Extract common widget properties based on type
      if (widget is StatefulWidget) {
        properties['isStateful'] = true;
      } else if (widget is StatelessWidget) {
        properties['isStateful'] = false;
      }

      // Add key if present
      if (widget.key != null) {
        properties['key'] = widget.key.toString();
      }

      return properties;
    } catch (e, stackTrace) {
      throw WidgetConversionException(
        'Failed to introspect widget properties: $e',
        widgetType: widget.runtimeType.toString(),
        originalException: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Converts a value to a JSON-serializable format
  static dynamic convertValue(dynamic value) {
    if (value == null) return null;

    // Basic types
    if (value is String || value is num || value is bool) {
      return value;
    }

    // DateTime
    if (value is DateTime) {
      return value.toIso8601String();
    }

    // Color
    if (value is Color) {
      return value.value;
    }

    // Enum
    if (value is Enum) {
      return value.name;
    }

    // List
    if (value is List) {
      return value.map(convertValue).toList();
    }

    // Map
    if (value is Map) {
      final converted = <String, dynamic>{};
      value.forEach((key, val) {
        converted[key.toString()] = convertValue(val);
      });
      return converted;
    }

    // EdgeInsets
    if (value is EdgeInsets) {
      return {
        'left': value.left,
        'top': value.top,
        'right': value.right,
        'bottom': value.bottom,
      };
    }

    // BoxShadow
    if (value is BoxShadow) {
      return {
        'color': value.color.value,
        'offset': {'dx': value.offset.dx, 'dy': value.offset.dy},
        'blurRadius': value.blurRadius,
        'spreadRadius': value.spreadRadius,
      };
    }

    // FontWeight
    if (value is FontWeight) {
      return FontWeight.values.indexOf(value);
    }

    // TextAlign
    if (value is TextAlign) {
      return value.name;
    }

    // For complex objects, try to convert to string
    return value.toString();
  }

  /// Gets the type hierarchy of a widget (simplified without reflection)
  ///
  /// [widget] The widget to analyze
  /// Returns a list of type names from most specific to most general
  static List<String> getTypeHierarchy(Widget widget) {
    final hierarchy = <String>[widget.runtimeType.toString()];

    // Add common Flutter widget hierarchy
    if (widget is StatefulWidget) {
      hierarchy.add('StatefulWidget');
    } else if (widget is StatelessWidget) {
      hierarchy.add('StatelessWidget');
    }

    hierarchy.add('Widget');
    hierarchy.add('DiagnosticableTree');
    hierarchy.add('Diagnosticable');
    hierarchy.add('Object');

    return hierarchy;
  }

  /// Checks if a widget implements a specific interface
  ///
  /// [widget] The widget to check
  /// [interfaceType] The interface type to check for
  /// Returns true if the widget implements the interface
  static bool implementsInterface(Widget widget, Type interfaceType) {
    // Simplified check without reflection
    if (interfaceType == JsonSerializableWidget) {
      return widget is JsonSerializableWidget;
    }

    if (interfaceType == StatefulWidget) {
      return widget is StatefulWidget;
    }

    if (interfaceType == StatelessWidget) {
      return widget is StatelessWidget;
    }

    return false;
  }

  /// Gets basic information about a widget
  ///
  /// [widget] The widget to analyze
  /// Returns basic widget information
  static Map<String, dynamic> getWidgetInfo(Widget widget) {
    return {
      'runtimeType': widget.runtimeType.toString(),
      'isStateful': widget is StatefulWidget,
      'isStateless': widget is StatelessWidget,
      'implementsJsonSerializable': widget is JsonSerializableWidget,
      'hasKey': widget.key != null,
      'keyType': widget.key?.runtimeType.toString(),
      'typeHierarchy': getTypeHierarchy(widget),
    };
  }

  /// Creates a basic widget configuration template
  ///
  /// [widgetTypeName] The widget type name
  /// Returns a template configuration with default values
  static Map<String, dynamic> createBasicTemplate(String widgetTypeName) {
    return {
      'widgetType': widgetTypeName.toLowerCase(),
      'schemaVersion': '1.0.0',
      'createdAt': DateTime.now().toIso8601String(),
    };
  }

  /// Analyzes widget properties that can be extracted without reflection
  ///
  /// [widget] The widget to analyze
  /// Returns available property information
  static Map<String, dynamic> analyzeWidget(Widget widget) {
    final analysis = <String, dynamic>{
      'basicInfo': getWidgetInfo(widget),
      'extractedProperties': extractProperties(widget),
    };

    // Add specific analysis for known widget types
    if (widget is JsonSerializableWidget) {
      final jsonWidget = widget as JsonSerializableWidget;
      analysis['jsonSerializableInfo'] = {
        'widgetType': jsonWidget.widgetType,
        'description': jsonWidget.description,
        'schemaVersion': jsonWidget.schemaVersion,
        'canValidate': true,
      };
    }

    return analysis;
  }

  /// Gets a default value for a given type name
  static dynamic getDefaultValueForType(String typeName) {
    switch (typeName.toLowerCase()) {
      case 'string':
        return '';
      case 'int':
      case 'integer':
        return 0;
      case 'double':
      case 'num':
        return 0.0;
      case 'bool':
      case 'boolean':
        return false;
      case 'datetime':
        return DateTime.now().toIso8601String();
      case 'color':
        return '#000000';
      case 'list':
        return [];
      case 'map':
        return {};
      default:
        return null;
    }
  }
}
