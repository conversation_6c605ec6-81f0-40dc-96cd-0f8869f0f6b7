library bi_directional_widget;

// Core interfaces and base classes
export 'src/core/widget_json_converter.dart';
export 'src/core/json_serializable_widget.dart';
export 'src/core/widget_introspector.dart';
export 'src/core/json_schema_validator.dart';

// Services
export 'src/services/widget_to_json_service.dart';
export 'src/services/json_to_widget_service.dart';
export 'src/services/widget_registry_service.dart';

// Models
export 'src/models/widget_config.dart';
export 'src/models/json_widget_schema.dart';


// Utilities
export 'src/utils/json_utils.dart';
export 'src/utils/widget_utils.dart';
export 'src/utils/color_utils.dart';

// Exceptions
export 'src/exceptions/widget_conversion_exception.dart';
export 'src/exceptions/json_validation_exception.dart';

// Complete Runtime Widget Tree Approach (Primary Approach)
export 'src/runtime/flexible_widget_serializer.dart';
export 'src/runtime/complete_runtime_calendar.dart';
