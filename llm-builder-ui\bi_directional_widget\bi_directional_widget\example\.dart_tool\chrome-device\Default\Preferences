{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 718, "left": 10, "maximized": true, "right": 1060, "top": 10, "work_area_bottom": 728, "work_area_left": 0, "work_area_right": 1366, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "3f780c72-d1f1-4f98-8ec7-ea8c0a06c91a", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.168", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.168\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.168\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.168\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.168\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.081369, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "ae130157-dfca-4a65-9080-77a83e89cab0"}}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "*****************"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "+qkcCeTC37IpOo8mTYAZ3L+43V8II4Oj+3YQ7PgLz8jVWTbi4EtBZLmiQYfTK+e2KKrBgJ9IYETFoUd2Vx5rsg=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "omnibox": {"shown_count_history_scope_promo": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13397825250314133", "last_fetch_success": "13397825250443743"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SAVED_TAB_GROUP": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:50403,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52682,*": {"expiration": "13405589472479307", "last_modified": "13397813472479320", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:56628,*": {"expiration": "13405595578645350", "last_modified": "13397819578645358", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57673,*": {"expiration": "13405603498471942", "last_modified": "13397827498471949", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13397813877218226", "setting": {"lastEngagementTime": 1.3397813877218212e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:50403,*": {"last_modified": "13397810552065676", "setting": {"lastEngagementTime": 1.3397810552065644e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:52682,*": {"last_modified": "13397813142972973", "setting": {"lastEngagementTime": 1.3397813142972944e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:56628,*": {"last_modified": "13397818167405520", "setting": {"lastEngagementTime": 1.3397818167405492e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:57673,*": {"last_modified": "13397826350705113", "setting": {"lastEngagementTime": 1.3397826350705086e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 13.799999999999995, "rawScore": 13.799999999999995}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.168", "creation_time": "*****************", "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.461421, "last_time_password_store_metrics_reported": **********.46189, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "64DDD25FB7033228033AA8AC522F82EB709E29CA4541805EBA51193C36E4D0C0"}, "extensions": {"ui": {"developer_mode": "DB156A3F6E456D851B180C09614F9001D225D332EFE705A7B19F777F5FE3743F"}}, "homepage": "275DE157D025C6D175A78FE26654BD85E1CA621CFA16733C3448E1C5BFE84856", "homepage_is_newtabpage": "F9FA42388CBDEACE1BBA1C28907E7B4D42ED87E27F2CE38F2028F3729F458E06", "session": {"restore_on_startup": "758A86EA1EC0A47C742EF37F34DF3555BC77E4A32F8D0F4491EC954ADAD02590", "startup_urls": "5E152D3B95FDF10E52445D7BE8631C20EACFD4249D217433F0B019FDC87804C1"}}, "browser": {"show_home_button": "13ECCC41058233F4B5A65C561278A4F7B13401F105C1B19A10AAED8420FEA762"}, "default_search_provider_data": {"template_url_data": "22F7DB8002524BB00D568AA561175EA476EAFCA8E2B5519C6BE1308BDE8A0CCD"}, "enterprise_signin": {"policy_recovery_token": "B8D53524570626D1145F3F5485F923CA3CD6BDBBCA685AB9958629AAD6B25BD7"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "7159D6A2376D2652B07A9ED7BE27179F23A265CCE07879A8EB81BBBA5E398F3D", "fignfifoniblkonapihmkfakmlgkbkcf": "F1EB7367302F66F2B87B802E45794A54A52605CAEAF82490374971816392E817", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "37EFCD4068A63186E73F342E62232DB2EED18110D05B66CB132765E4E1067CFE", "nkeimhogjdpnpccoofpliimaahmaaome": "36FFE712D79C1CD3880CDB6A7922E3EE3D09D1C309CD6A712FF87AAE185AFFBB"}, "ui": {"developer_mode": "69FABB9DC5F14F7E13975B8FC550C4995AE3147BF820E246979B97A5A3434D7C"}}, "google": {"services": {"account_id": "7E3560DD2FEE98106960BE449F966E3207B946DB6A6DC8889C14C49D28CD119D", "last_signed_in_username": "2B18492218ABC525176B52BFFEA63900681BBDA047F7C7872BF111C12E888F9D", "last_username": "DC0924AD68F798BD56EC8BF0C86618869A7CF2DDAECC1BBBCB7B8B0D830BB389"}}, "homepage": "0EDD9BC0A838AEB62C82D8CD45126B2F149F3DC0CEE59B312D08845FA630849B", "homepage_is_newtabpage": "D5CDF59ACB09B1057645384375F54F7523996FC0923C06BBCFDE59415DF9581A", "media": {"cdm": {"origin_data": "B2C4507048C89DF0A2F482C3730D7F321B241B2591896AB2728BF422E2EB6AF8"}, "storage_id_salt": "4D337084689E4152D14F6C16BB1151C901BD46F71952FD0744D6568C37903588"}, "module_blocklist_cache_md5_digest": "08CC6CD6CE02D2385ABD48CB34FCA0C11BB3835AD493D1A3CFDD216FDA6F0E3A", "pinned_tabs": "C0908F5A3F6A31F5E3DE65D6B1794EBDEE5051859E7C4D1AA0E5DE151F674E19", "prefs": {"preference_reset_time": "453402483407F211165859861723E382511B5E40B561090CF024168DC589A9FF"}, "safebrowsing": {"incidents_sent": "6EC1CC0D21C43B20200C991980967899B95C075847E2DD43F591F6C6DCD375D7"}, "search_provider_overrides": "CB2C5920A40948DFB0E66AAD0EC783D47D8B06CCF0293578027648323C80703F", "session": {"restore_on_startup": "706311C8204136A30632DB9120841762B2F4B08A12C83A55195960E1ECAD45AC", "startup_urls": "62B13FD933C6F28A503C4AC33A2982408E125F6B4EB0798DA7E182F84D13A00B"}}}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13398068594993059", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13397809394", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQpsiqsMmn5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEPTIqrDJp+YX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13397702399000000", "uma_in_sql_start_time": "13397809394466881"}, "sessions": {"event_log": [{"crashed": false, "time": "13397809394461301", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397810880230079", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397811037228579", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397813472017541", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397813513786209", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397819578626161", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397825240302586", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397827048846020", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"mumbai heavy rainfall alert\",\"samsung one ui 8 watch\",\"tata nexon 2025 features mileage\",\"national sports bill\",\"ism e yaran drama episode 22\",\"ssc phase 13 exam admit card\",\"paytm share\",\"red bull racing\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIkk4SFQoRVHJlbmRpbmcgc2VhcmNoZXMoCg\\u003d\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\\u003d\",\"zl\":10002}],\"google:suggesteventid\":\"-6455241814938387870\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\"]}]"}}