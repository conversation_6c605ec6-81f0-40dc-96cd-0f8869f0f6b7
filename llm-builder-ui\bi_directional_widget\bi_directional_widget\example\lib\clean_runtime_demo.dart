import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bi_directional_widget/src/runtime/complete_runtime_calendar.dart';

/// Tabbed Runtime Demo - Better UX with Tab-based Navigation
/// Four tabs: Original Widget, JSON Output, Recreated Widget, Comparison
class TabbedRuntimeDemo extends StatefulWidget {
  const TabbedRuntimeDemo({super.key});

  @override
  State<TabbedRuntimeDemo> createState() => _TabbedRuntimeDemoState();
}

class _TabbedRuntimeDemoState extends State<TabbedRuntimeDemo>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  // Current size selection
  String _selectedSize = 'Medium';
  String _selectedMonth = 'July';
  DateTime? _selectedDate = DateTime(2025, 7, 21);

  // Step 1: Original Widget
  Widget? _originalWidget;
  
  // Step 2: JSON String
  String _jsonOutput = 'Click "Generate JSON" to see the complete widget tree structure';
  bool _isJsonGenerated = false;
  
  // Step 3: Recreated Widget
  Widget? _recreatedWidget;
  bool _isWidgetRecreated = false;
  
  // Statistics
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    // Listen to tab changes to update app bar actions
    _tabController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _createOriginalWidget() {
    setState(() {
      switch (_selectedSize) {
        case 'Small':
          _originalWidget = StatefulCalendarWidget(
            size: CalendarSize.small,
            initialSelectedDate: _selectedDate,
            selectedColor: Colors.blue,
            todayColor: Colors.orange.shade100,
            initialSelectedMonth: _selectedMonth,
          );
          break;
        case 'Medium':
          _originalWidget = StatefulCalendarWidget(
            size: CalendarSize.medium,
            initialSelectedDate: _selectedDate,
            selectedColor: Colors.blue,
            todayColor: Colors.orange.shade100,
            initialSelectedMonth: _selectedMonth,
          );
          break;
        case 'Large':
          _originalWidget = StatefulCalendarWidget(
            size: CalendarSize.large,
            initialSelectedDate: _selectedDate,
            selectedColor: Colors.blue,
            todayColor: Colors.orange.shade100,
            initialSelectedMonth: _selectedMonth,
          );
          break;
      }
      
      // Reset dependent states when widget changes
      _jsonOutput = 'Click "Generate JSON" to see the complete widget tree structure';
      _isJsonGenerated = false;
      _recreatedWidget = null;
      _isWidgetRecreated = false;
      _stats = {};
    });
  }

  void _generateJson() {
    if (_originalWidget != null) {
      try {
        final startTime = DateTime.now().microsecondsSinceEpoch;
        final jsonString = CompleteRuntimeCalendar.widgetToJson(_originalWidget!);
        final endTime = DateTime.now().microsecondsSinceEpoch;
        
        setState(() {
          _jsonOutput = jsonString;
          _isJsonGenerated = true;
          _stats = {
            'serializationTime': endTime - startTime,
            'jsonSize': jsonString.length,
            'jsonLines': jsonString.split('\n').length,
          };
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ JSON generated successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
        
        // Auto-navigate to JSON tab
        _tabController.animateTo(1);
      } catch (e) {
        setState(() {
          _jsonOutput = 'JSON GENERATION FAILED!\n\nError: $e';
          _isJsonGenerated = false;
          _stats = {};
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ JSON generation failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _recreateWidget() {
    if (_isJsonGenerated && !_jsonOutput.startsWith('JSON GENERATION FAILED')) {
      try {
        final startTime = DateTime.now().microsecondsSinceEpoch;
        final recreatedWidget = CompleteRuntimeCalendar.jsonToWidget(_jsonOutput);
        final endTime = DateTime.now().microsecondsSinceEpoch;
        
        if (recreatedWidget != null) {
          setState(() {
            _recreatedWidget = recreatedWidget;
            _isWidgetRecreated = true;
            _stats['deserializationTime'] = endTime - startTime;
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Widget recreated successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
          
          // Auto-navigate to recreated widget tab
          _tabController.animateTo(2);
        } else {
          throw Exception('Deserialization returned null widget');
        }
      } catch (e) {
        setState(() {
          _recreatedWidget = Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'WIDGET RECREATION FAILED!\n\nError: $e',
              style: const TextStyle(color: Colors.red),
            ),
          );
          _isWidgetRecreated = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Widget recreation failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⚠️ Generate JSON first!'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _performCompleteDemo() {
    _generateJson();
    Future.delayed(const Duration(milliseconds: 800), () {
      _recreateWidget();
      Future.delayed(const Duration(milliseconds: 500), () {
        // Navigate to comparison tab
        _tabController.animateTo(3);
      });
    });
  }

  void _clearAll() {
    setState(() {
      _jsonOutput = 'Click "Generate JSON" to see the complete widget tree structure';
      _isJsonGenerated = false;
      _recreatedWidget = null;
      _isWidgetRecreated = false;
      _stats = {};
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🧹 All data cleared!'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _copyJsonToClipboard() {
    if (_isJsonGenerated) {
      Clipboard.setData(ClipboardData(text: _jsonOutput));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('📋 JSON copied to clipboard!'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  List<Widget> _getAppBarActions() {
    switch (_tabController.index) {
      case 0: // Original Widget tab
        return [
          IconButton(
            onPressed: _generateJson,
            icon: const Icon(Icons.code),
            tooltip: 'Generate JSON',
          ),
          IconButton(
            onPressed: _performCompleteDemo,
            icon: const Icon(Icons.play_arrow),
            tooltip: 'Run Complete Demo',
          ),
        ];
      case 1: // JSON tab
        return [
          IconButton(
            onPressed: _isJsonGenerated ? _copyJsonToClipboard : null,
            icon: const Icon(Icons.copy),
            tooltip: 'Copy JSON',
          ),
          IconButton(
            onPressed: _recreateWidget,
            icon: const Icon(Icons.widgets),
            tooltip: 'Recreate Widget',
          ),
        ];
      case 2: // Recreated Widget tab
        return [
          IconButton(
            onPressed: () => _tabController.animateTo(3),
            icon: const Icon(Icons.compare_arrows),
            tooltip: 'Compare Widgets',
          ),
        ];
      case 3: // Comparison tab
        return [
          IconButton(
            onPressed: _clearAll,
            icon: const Icon(Icons.clear_all),
            tooltip: 'Clear All',
          ),
        ];
      default:
        return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create widget on first build when context is available
    if (_originalWidget == null) {
      _createOriginalWidget();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Runtime Calendar Demo'),
        backgroundColor: Colors.blue.shade100,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.calendar_today),
              text: 'Original',
              iconMargin: const EdgeInsets.only(bottom: 4),
            ),
            Tab(
              icon: Icon(
                Icons.code,
                color: _isJsonGenerated ? Colors.green : null,
              ),
              text: 'JSON',
              iconMargin: const EdgeInsets.only(bottom: 4),
            ),
            Tab(
              icon: Icon(
                Icons.widgets,
                color: _isWidgetRecreated ? Colors.green : null,
              ),
              text: 'Recreated',
              iconMargin: const EdgeInsets.only(bottom: 4),
            ),
            Tab(
              icon: const Icon(Icons.compare_arrows),
              text: 'Compare',
              iconMargin: const EdgeInsets.only(bottom: 4),
            ),
          ],
        ),
        actions: _getAppBarActions(),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOriginalWidgetTab(),
          _buildJsonTab(),
          _buildRecreatedWidgetTab(),
          _buildComparisonTab(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildOriginalWidgetTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Controls
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Calendar Size:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(width: 16),
                    DropdownButton<String>(
                      value: _selectedSize,
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          setState(() {
                            _selectedSize = newValue;
                          });
                          _createOriginalWidget();
                        }
                      },
                      items: ['Small', 'Medium', 'Large']
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                    ),
                    const Spacer(),
                    ElevatedButton.icon(
                      onPressed: _generateJson,
                      icon: const Icon(Icons.code),
                      label: const Text('Generate JSON'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade100,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
       
          
          // Original Widget Display
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Original Widget ($_selectedSize)',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                if (_originalWidget != null)
                  Center(child: _originalWidget!)
                else
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
              ],
            ),
          ),
          
        
        ],
      ),
    );
  }

  Widget _buildJsonTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
               
                const Spacer(),
                if (_isJsonGenerated) ...[
                  IconButton(
                    onPressed: _copyJsonToClipboard,
                    icon: const Icon(Icons.copy),
                    tooltip: 'Copy to Clipboard',
                  ),
                  ElevatedButton.icon(
                    onPressed: _recreateWidget,
                    icon: const Icon(Icons.widgets),
                    label: const Text('Recreate Widget'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade100,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
        
          
          Container(
            width: double.infinity,
            height: MediaQuery.of(context).size.height * 0.6,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
               
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: SingleChildScrollView(
                      child: SelectableText(
                        _jsonOutput,
                        style: TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                          color: _isJsonGenerated ? Colors.black : Colors.grey,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecreatedWidgetTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  Icons.widgets,
                  size: 24,
                  color: _isWidgetRecreated ? Colors.green : null,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Recreated from JSON',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (_isWidgetRecreated)
                  ElevatedButton.icon(
                    onPressed: () => _tabController.animateTo(3),
                    icon: const Icon(Icons.compare_arrows),
                    label: const Text('Compare'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple.shade100,
                    ),
                  ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          Container(
            width: double.infinity,
            height: MediaQuery.of(context).size.height * 0.6,
            padding: const EdgeInsets.all(16),
            child: _recreatedWidget != null
                ? Center(child: _recreatedWidget!)
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.widgets, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'Generate JSON first, then\nclick "Recreate Widget"',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _clearAll,
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Reset Demo'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade100,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
        
          
          Row(
            children: [
              // Original Widget
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const Text(
                        'Original Widget',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      if (_originalWidget != null)
                        _originalWidget!
                      else
                        const Text('No widget created'),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Recreated Widget
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Recreated Widget',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(width: 8),
                          if (_isWidgetRecreated)
                            const Icon(Icons.check_circle, color: Colors.green, size: 20),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_recreatedWidget != null)
                        _recreatedWidget!
                      else
                        const Text(
                          'Generate JSON and recreate widget first',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 0:
        return FloatingActionButton.extended(
          onPressed: _performCompleteDemo,
          icon: const Icon(Icons.play_arrow),
          label: const Text('Run Demo'),
          backgroundColor: Colors.green,
        );
      case 1:
        if (!_isJsonGenerated) {
          return FloatingActionButton.extended(
            onPressed: _generateJson,
            icon: const Icon(Icons.code),
            label: const Text('Generate'),
            backgroundColor: Colors.blue,
          );
        }
        return null;
      case 2:
        if (_isJsonGenerated && !_isWidgetRecreated) {
          return FloatingActionButton.extended(
            onPressed: _recreateWidget,
            icon: const Icon(Icons.widgets),
            label: const Text('Recreate'),
            backgroundColor: Colors.orange,
          );
        }
        return null;
      default:
        return null;
    }
  }
}