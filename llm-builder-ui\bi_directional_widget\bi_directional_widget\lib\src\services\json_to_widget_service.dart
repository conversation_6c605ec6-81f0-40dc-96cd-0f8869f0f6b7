import 'package:flutter/material.dart';
import 'dart:convert';
import '../exceptions/widget_conversion_exception.dart';
import '../exceptions/json_validation_exception.dart';
import 'widget_registry_service.dart';

/// Service for converting JSON to widgets
/// 
/// This service provides methods to create widget instances from JSON representations
/// using registered converters.
class JsonToWidgetService {
  static final WidgetRegistryService _registry = WidgetRegistryService();

  /// Converts JSON to a widget using registered converters
  /// 
  /// [json] The JSON configuration as a Map
  /// Returns the created widget instance
  /// Throws [WidgetConversionException] if conversion fails
  /// Throws [JsonValidationException] if JSON is invalid
  static Widget convert(Map<String, dynamic> json) {
    try {
      // Validate basic JSON structure
      if (!json.containsKey('widgetType')) {
        throw JsonValidationException(
          'JSON must contain a "widgetType" field',
          json: json,
        );
      }

      final widgetType = json['widgetType'] as String;
      
      // Get the appropriate converter
      final converter = _registry.getConverter(widgetType);
      if (converter == null) {
        throw WidgetConversionException(
          'No converter registered for widget type: $widgetType',
          widgetType: widgetType,
        );
      }

      // Validate JSON against the converter's schema
      final errors = <String>[];
      if (!converter.validateJson(json, errors: errors)) {
        throw JsonValidationException(
          'JSON validation failed for widget type: $widgetType',
          errors: errors,
          json: json,
          widgetType: widgetType,
        );
      }

      // Convert JSON to widget
      return converter.jsonToWidget(json);
    } catch (e, stackTrace) {
      if (e is WidgetConversionException || e is JsonValidationException) {
        rethrow;
      }
      
      throw WidgetConversionException(
        'Failed to convert JSON to widget: $e',
        widgetType: json['widgetType']?.toString(),
        originalException: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Converts a JSON string to a widget
  /// 
  /// [jsonString] The JSON configuration as a String
  /// Returns the created widget instance
  /// Throws [WidgetConversionException] if conversion fails
  static Widget convertFromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return convert(json);
    } catch (e, stackTrace) {
      throw WidgetConversionException(
        'Failed to parse JSON string: $e',
        originalException: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Converts multiple JSON configurations to widgets
  /// 
  /// [jsonList] List of JSON configurations
  /// Returns a list of created widget instances
  /// Throws [WidgetConversionException] if any conversion fails
  static List<Widget> convertMultiple(List<Map<String, dynamic>> jsonList) {
    final results = <Widget>[];
    
    for (int i = 0; i < jsonList.length; i++) {
      try {
        results.add(convert(jsonList[i]));
      } catch (e) {
        throw WidgetConversionException(
          'Failed to convert JSON at index $i: $e',
          originalException: e,
        );
      }
    }
    
    return results;
  }

  /// Converts a JSON tree to a widget tree recursively
  /// 
  /// [json] The root JSON configuration
  /// [maxDepth] Maximum depth to traverse (prevents infinite recursion)
  /// Returns the created widget tree
  static Widget convertTree(Map<String, dynamic> json, {int maxDepth = 10}) {
    if (maxDepth <= 0) {
      return const Text('Maximum depth reached');
    }

    // Convert the current widget
    final widget = convert(json);
    
    // If there are children, we would need to rebuild the widget with the children
    // This is a simplified implementation - in practice, you'd need to handle
    // different widget types that accept children differently
    if (json.containsKey('children')) {
      final childrenJson = json['children'] as List;
      final children = childrenJson.map((childJson) => 
        convertTree(childJson as Map<String, dynamic>, maxDepth: maxDepth - 1)
      ).toList();
      
      // This is a simplified approach - in reality, you'd need to handle
      // different widget types that accept children in different ways
      return _wrapWithChildren(widget, children);
    }
    
    return widget;
  }

  /// Wraps a widget with its children (simplified implementation)
  /// 
  /// This is a basic implementation that handles common cases.
  /// A complete implementation would need to handle all widget types.
  static Widget _wrapWithChildren(Widget parent, List<Widget> children) {
    // This is a very simplified approach
    // In practice, you'd need to handle different widget types appropriately
    if (children.isEmpty) return parent;
    
    // For demonstration, wrap in a Column if multiple children
    if (children.length > 1) {
      return Column(children: children);
    }
    
    return children.first;
  }

  /// Validates JSON before conversion
  /// 
  /// [json] The JSON to validate
  /// [errors] Optional list to collect validation errors
  /// Returns true if JSON is valid for conversion, false otherwise
  static bool validateJson(Map<String, dynamic> json, {List<String>? errors}) {
    errors?.clear();
    
    // Check for required fields
    if (!json.containsKey('widgetType')) {
      errors?.add('Missing required field: widgetType');
      return false;
    }

    final widgetType = json['widgetType'];
    if (widgetType is! String) {
      errors?.add('widgetType must be a string');
      return false;
    }

    // Check if converter is registered
    final converter = _registry.getConverter(widgetType);
    if (converter == null) {
      errors?.add('No converter registered for widget type: $widgetType');
      return false;
    }

    // Validate using the converter's schema
    return converter.validateJson(json, errors: errors);
  }

  /// Gets the JSON schema for a widget type
  /// 
  /// [widgetType] The widget type to get schema for
  /// Returns the JSON schema or null if widget type is not registered
  static Map<String, dynamic>? getSchema(String widgetType) {
    final converter = _registry.getConverter(widgetType);
    return converter?.jsonSchema;
  }

  /// Gets all available JSON schemas
  /// 
  /// Returns a map of widget type to JSON schema
  static Map<String, Map<String, dynamic>> getAllSchemas() {
    final schemas = <String, Map<String, dynamic>>{};
    final converters = _registry.getAllConverters();
    
    for (final entry in converters.entries) {
      schemas[entry.key] = entry.value.jsonSchema;
    }
    
    return schemas;
  }

  /// Creates a widget with default configuration
  /// 
  /// [widgetType] The type of widget to create
  /// Returns a widget with default properties
  /// Throws [WidgetConversionException] if widget type is not registered
  static Widget createDefault(String widgetType) {
    final converter = _registry.getConverter(widgetType);
    if (converter == null) {
      throw WidgetConversionException(
        'No converter registered for widget type: $widgetType',
        widgetType: widgetType,
      );
    }

    // Create a minimal JSON with just the widget type
    final defaultJson = {
      'widgetType': widgetType,
    };

    return converter.jsonToWidget(defaultJson);
  }

  /// Merges JSON configurations
  /// 
  /// [base] The base JSON configuration
  /// [override] The override JSON configuration
  /// Returns the merged configuration with override taking precedence
  static Map<String, dynamic> mergeConfigurations(
    Map<String, dynamic> base,
    Map<String, dynamic> override,
  ) {
    final merged = Map<String, dynamic>.from(base);
    
    override.forEach((key, value) {
      if (value != null) {
        merged[key] = value;
      }
    });
    
    return merged;
  }

  /// Gets conversion statistics
  /// 
  /// Returns information about available converters and conversion capabilities
  static Map<String, dynamic> getStats() {
    final registryStats = _registry.getStats();
    
    return {
      'availableConverters': registryStats['totalConverters'],
      'supportedTypes': registryStats['registeredTypes'],
      'availableSchemas': getAllSchemas().keys.toList(),
      'registryStats': registryStats,
    };
  }
}
