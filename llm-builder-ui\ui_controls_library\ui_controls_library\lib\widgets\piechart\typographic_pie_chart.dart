import 'package:flutter/material.dart';
import 'dart:convert';

import 'package:ui_controls_library/widgets/piechart/common/common_widgets.dart';
import 'package:ui_controls_library/widgets/piechart/common/config.dart';



// class PieChartUIBuilder extends StatefulWidget {
//   final Function onPressed;
//   final double chartRadius;

//   const PieChartUIBuilder({
//     super.key,
//     required this.onPressed,
//     required this.chartRadius,
//   });

//   @override
//   State<PieChartUIBuilder> createState() => PieChartUIBuilderState();
// }

// class PieChartUIBuilderState extends State<PieChartUIBuilder> {
//   late JsonWidget screenWidgetTree;
//   String jsonOutput = '';

//   final Map<String, double> dataMap = {
//     'Point 1': 1,
//     'Point 2': 1,
//     'Point 3': 1,
//     'Point 4': 1,
//     'Point 5': 1,
//     'Point 6': 1,
//     'Point 7': 1,
//     'Point 8': 1,
//   };

//   final List<Color> colorList = [
//     Color(0xFF0D47A1),
//     Color(0xFF1565C0),
//     Color(0xFF1976D2),
//     Color(0xFF1E88E5),
//     Color(0xFF42A5F5),
//     Color(0xFF64B5F6),
//     Color(0xFF90CAF9),
//     Color(0xFFBBDEFB),
//   ];

//   JsonWidget buildChartSection({
//     required String sectionTitle,
//     required double headingFontSize,
//     required double bodyFontSize,
//     required double labelFontSize,
//     required double chartRadius,
//   }) {
//     return CommonColumn(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         CommonText(sectionTitle, fontSize: 24, fontWeight: FontWeight.normal),
//         CommonSizedBox(height: 12),
//         CommonCard(
//           elevation: 0.5,
//           borderRadius: 12,
//           child: CommonColumn(
//             children: [
//               CommonText("Temperature theme", fontSize: bodyFontSize, fontWeight: FontWeight.w600),
//               CommonSizedBox(height: 8),
//               CommonRow(
//                 children: [
//                   CommonExpanded(
//                     flex: 5,
//                     child: CommonPieChart(
//                       dataMap: dataMap,
//                       colorList: colorList,
//                       chartRadius: chartRadius,
//                       ringStrokeWidth: chartRadius * 0.2,
//                       centerText: "Total Value\n\$9,999.99",
//                     ),
//                   ),
//                   CommonSizedBox(width: 24),
//                   CommonExpanded(
//                     flex: 4,
//                     child: CommonLegend(
//                       labels: dataMap.keys.toList(),
//                       colors: colorList,
//                       fontSize: labelFontSize,
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ),
//         CommonSizedBox(height: 24),
//         CommonText("Properties", fontWeight: FontWeight.bold, fontSize: bodyFontSize, color: Colors.black),
//         CommonText("Heading 1 medium ${headingFontSize.toInt()}", fontSize: headingFontSize, fontWeight: FontWeight.w500),
//         CommonText("Body 1 Regular ${bodyFontSize.toInt()}", fontSize: bodyFontSize),
//         CommonText("Label - Regular ${labelFontSize.toInt()}", fontSize: labelFontSize, color: Colors.grey[700]),
//       ],
//     );
//   }

//   void _rebuildWidgetTree() {
//     screenWidgetTree = CommonColumn(
//       children: [
//         CommonText('Properties - Typography', fontSize: 33, fontWeight: FontWeight.bold),
//         CommonSizedBox(height: 32),
//         CommonRow(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             CommonExpanded(
//               child: buildChartSection(
//                 sectionTitle: "Chart",
//                 headingFontSize: 16,
//                 bodyFontSize: 14,
//                 labelFontSize: 12,
//                 chartRadius: widget.chartRadius,
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }

//   @override
//   void initState() {
//     super.initState();
//     _rebuildWidgetTree();
//   }

//   @override
//   void didUpdateWidget(covariant PieChartUIBuilder oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.chartRadius != widget.chartRadius) {
//       setState(() {
//         _rebuildWidgetTree();
//       });
//     }
//   }

//   Future<dynamic> generateJson() async {
//     final generated = screenWidgetTree.toJson();
//     setState(() {
//       jsonOutput = const JsonEncoder.withIndent('  ').convert(generated);
//     });
//     print("=== Serialized Widget JSON ===\n$jsonOutput");
//     return generated;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(32),
//       child: screenWidgetTree.build(),
//     );
//   }
// }



class PieChartUIBuilder extends StatefulWidget {
  final Function onPressed;
  final ChartSizeConfig config;

  const PieChartUIBuilder({
    super.key,
    required this.onPressed,
    required this.config,
  });

  @override
  State<PieChartUIBuilder> createState() => PieChartUIBuilderState();
}

class PieChartUIBuilderState extends State<PieChartUIBuilder> {
  late JsonWidget screenWidgetTree;

  final Map<String, double> dataMap = {
    'Point 1': 1, 'Point 2': 1, 'Point 3': 1, 'Point 4': 1,
    'Point 5': 1, 'Point 6': 1, 'Point 7': 1, 'Point 8': 1,
  };

  final List<Color> colorList = [
    Color(0xFF0D47A1), Color(0xFF1565C0), Color(0xFF1976D2), Color(0xFF1E88E5),
    Color(0xFF42A5F5), Color(0xFF64B5F6), Color(0xFF90CAF9), Color(0xFFBBDEFB),
  ];

  JsonWidget buildChartSection({required ChartSizeConfig c}) {
    // final c = widget.config;

    return CommonColumn(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(c.size?.name.toUpperCase()??"", fontSize: 24),
       CommonRow(children: [
        
        CommonExpanded(
          flex: 4,
          child:
        CommonCard(
          elevation: 0.5,
          borderRadius: 12,
          borderWidth: c.borderThikness,
          child: CommonColumn(
            children: [
              CommonText("Temperature theme", fontSize: c.bodyFontSize, fontWeight: FontWeight.w600),
              CommonSizedBox(height: 8),
              CommonRow(
                children: [
                  CommonExpanded(
                    flex: 5,
                    child: CommonPieChart(
                      dataMap: dataMap,
                      colorList: colorList,
                      chartRadius: c.chartRadius??0,
                      ringStrokeWidth: (c.chartRadius??0) * 0.2,
                      centerText: "Total Value\n\$9,999.99",
                    ),
                  ),
                  CommonSizedBox(width: 24),
                  CommonExpanded(
                    flex: 4,
                    child: CommonLegend(
                      labels: dataMap.keys.toList(),
                      colors: colorList,
                      fontSize: c.labelFontSize??0,
                    ),
                  ),
                ],
              ),
            ],
          ),
        )),
          CommonExpanded(
          flex: 6,
          child: CommonSizedBox(width: 20),),
       ]),
        CommonSizedBox(height: 24),
        CommonText("Properties", fontWeight: FontWeight.bold, fontSize: c.bodyFontSize, color: Colors.black),
        CommonText("Heading 1 Medium ${c.headingFontSize?.toInt()}",
            fontSize: c.headingFontSize, fontWeight: FontWeight.w500),
        CommonText("Body 1 Regular ${c.bodyFontSize?.toInt()}",
            fontSize: c.bodyFontSize),
        CommonText("Label - Regular ${c.labelFontSize?.toInt()}",
            fontSize: c.labelFontSize, color: Colors.grey[700]),
      ],
    );
  }

  void _rebuildWidgetTree() {
    final c = widget.config;
    screenWidgetTree = CommonColumn(
      children: [
        CommonText(c.propertyType??"", fontSize: 33, fontWeight: FontWeight.bold),
        CommonSizedBox(height: 32),
        buildChartSection(c: c),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    _rebuildWidgetTree();
  }

  @override
  void didUpdateWidget(covariant PieChartUIBuilder oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config != widget.config) {
      setState(() => _rebuildWidgetTree());
    }
  }

  Future<dynamic> generateJson() async {
    final generated = screenWidgetTree.toJson();
    print("=== Serialized Widget JSON ===\n${const JsonEncoder.withIndent('  ').convert(generated)}");
    return generated;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32),
      child: screenWidgetTree.build(),
    );
  }
}
