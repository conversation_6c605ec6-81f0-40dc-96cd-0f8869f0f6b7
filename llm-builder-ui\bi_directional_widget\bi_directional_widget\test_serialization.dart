import 'package:flutter/material.dart';
import 'lib/src/runtime/complete_runtime_calendar.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: TestSerializationPage(),
    );
  }
}

class TestSerializationPage extends StatefulWidget {
  @override
  _TestSerializationPageState createState() => _TestSerializationPageState();
}

class _TestSerializationPageState extends State<TestSerializationPage> {
  String jsonOutput = '';
  Widget? recreatedWidget;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Test Calendar Serialization')),
      body: Column(
        children: [
          Row(
            children: [
              ElevatedButton(
                onPressed: () {
                  final calendar = CompleteRuntimeCalendar.createCalendar(
                    context: context,
                    size: CalendarSize.medium,
                    selectedDate: DateTime(2025, 7, 21),
                    selectedColor: Colors.blue,
                    todayColor: Colors.orange.shade100,
                    selectedMonth: 'July',
                    onDateSelected: (DateTime date) {
                      print('Date selected: $date');
                    },
                    onMonthChanged: (String month) {
                      print('Month changed: $month');
                    },
                  );

                  final json = CompleteRuntimeCalendar.widgetToJson(calendar);
                  setState(() {
                    jsonOutput = json;
                  });
                  print('JSON Output:');
                  print(json);
                },
                child: Text('Test Serialization'),
              ),
              SizedBox(width: 10),
              ElevatedButton(
                onPressed: () {
                  if (jsonOutput.isNotEmpty) {
                    final widget = CompleteRuntimeCalendar.jsonToWidget(jsonOutput);
                    setState(() {
                      recreatedWidget = widget;
                    });
                    print('Widget recreated successfully');
                  }
                },
                child: Text('Test Deserialization'),
              ),
            ],
          ),
          Expanded(
            child: Row(
              children: [
                // JSON Output
                Expanded(
                  child: Column(
                    children: [
                      Text('JSON Output:', style: TextStyle(fontWeight: FontWeight.bold)),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: EdgeInsets.all(8),
                            child: Text(
                              jsonOutput,
                              style: TextStyle(fontFamily: 'monospace', fontSize: 10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Recreated Widget
                Expanded(
                  child: Column(
                    children: [
                      Text('Recreated Widget:', style: TextStyle(fontWeight: FontWeight.bold)),
                      Expanded(
                        child: recreatedWidget != null
                            ? Center(child: recreatedWidget!)
                            : Center(child: Text('Click "Test Deserialization" to see recreated widget')),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
