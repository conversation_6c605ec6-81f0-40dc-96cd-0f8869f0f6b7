import 'package:flutter/material.dart';
import 'lib/src/runtime/complete_runtime_calendar.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: TestSerializationPage(),
    );
  }
}

class TestSerializationPage extends StatefulWidget {
  @override
  _TestSerializationPageState createState() => _TestSerializationPageState();
}

class _TestSerializationPageState extends State<TestSerializationPage> {
  String jsonOutput = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Test Calendar Serialization')),
      body: Column(
        children: [
          ElevatedButton(
            onPressed: () {
              final calendar = CompleteRuntimeCalendar.createCalendar(
                context: context,
                size: CalendarSize.medium,
                selectedDate: DateTime(2025, 7, 21),
                selectedColor: Colors.blue,
                todayColor: Colors.orange.shade100,
                selectedMonth: 'July',
                onDateSelected: (DateTime date) {
                  print('Date selected: $date');
                },
                onMonthChanged: (String month) {
                  print('Month changed: $month');
                },
              );
              
              final json = CompleteRuntimeCalendar.widgetToJson(calendar);
              setState(() {
                jsonOutput = json;
              });
              print('JSON Output:');
              print(json);
            },
            child: Text('Test Serialization'),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  jsonOutput,
                  style: TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
