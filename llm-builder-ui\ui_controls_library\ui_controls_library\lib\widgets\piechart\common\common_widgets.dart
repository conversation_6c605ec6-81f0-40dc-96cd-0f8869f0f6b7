import 'package:flutter/material.dart';
import 'package:pie_chart/pie_chart.dart';

/// Abstract class for all widgets that can be represented as JSON.
abstract class JsonWidget {
  Widget build();
  Map<String, dynamic> toJson();
}

/// Text widget wrapper
class CommonText extends JsonWidget {
  final String text;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final TextDecoration? textDecoration;

  CommonText(this.text, {this.fontSize, this.fontWeight, this.color, this.textAlign,this.textDecoration});

  @override
  Widget build() {
    return Text(
      text,
      textAlign: textAlign,
      
      style: TextStyle(
        decoration: textDecoration,
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Text",
        "text": text,
        if (fontSize != null) "fontSize": fontSize,
        if (fontWeight != null) "fontWeight": fontWeight.toString(),
        if (color != null) "color": '#${color!.value.toRadixString(16)}',
        if (textAlign != null) "textAlign": textAlign.toString(),
        if(textDecoration!=null) "textDecoration":textDecoration.toString()
      };
}

/// Card widget wrapper
class CommonCard extends JsonWidget {
  final JsonWidget child;
  final double? elevation;
  final double? borderRadius;
  final double? borderWidth;

  CommonCard({required this.child, this.elevation, this.borderRadius,this.borderWidth});

  @override
  Widget build() {
    return Card(
      elevation: elevation ?? 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 8),
        side: BorderSide(
      color: const Color.fromARGB(255, 237, 235, 235),   // Border color
      width: borderWidth??0,           // Border width
    ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child.build(),
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Card",
        if (elevation != null) "elevation": elevation,
        if (borderRadius != null) "borderRadius": borderRadius,
        if(borderWidth!=null) "borderWidth":borderWidth,
        "child": child.toJson(),
      };
}

/// Row widget wrapper
class CommonRow extends JsonWidget {
  final List<JsonWidget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  CommonRow({
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  });

  @override
  Widget build() {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children.map((e) => e.build()).toList(),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Row",
        "mainAxisAlignment": mainAxisAlignment.toString(),
        "crossAxisAlignment": crossAxisAlignment.toString(),
        "children": children.map((e) => e.toJson()).toList(),
      };
}

/// Column widget wrapper
class CommonColumn extends JsonWidget {
  final List<JsonWidget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  CommonColumn({
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build() {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children.map((e) => e.build()).toList(),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Column",
        "mainAxisAlignment": mainAxisAlignment.toString(),
        "crossAxisAlignment": crossAxisAlignment.toString(),
        "children": children.map((e) => e.toJson()).toList(),
      };
}

/// SizedBox widget wrapper
class CommonSizedBox extends JsonWidget {
  final double? width;
  final double? height;

  CommonSizedBox({this.width, this.height});

  @override
  Widget build() => SizedBox(width: width, height: height);

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "SizedBox",
        if (width != null) "width": width,
        if (height != null) "height": height,
      };
}

/// Expanded widget wrapper
class CommonExpanded extends JsonWidget {
  final JsonWidget child;
  final int flex;

  CommonExpanded({required this.child, this.flex = 1});

  @override
  Widget build() => Expanded(flex: flex, child: child.build());

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Expanded",
        "flex": flex,
        "child": child.toJson(),
      };
}

/// Container widget wrapper
class CommonContainer extends JsonWidget {
  final JsonWidget? child;
  final double? width;
  final double? height;
  final Color? color;

  CommonContainer({this.child, this.width, this.height, this.color});

  @override
  Widget build() => Container(
        width: width,
        height: height,
        color: color,
        child: child?.build(),
      );

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Container",
        if (width != null) "width": width,
        if (height != null) "height": height,
        if (color != null) "color": '#${color!.value.toRadixString(16)}',
        if (child != null) "child": child!.toJson(),
      };
}

/// Pie Chart widget wrapper (Leaf node using an external library)
class CommonPieChart extends JsonWidget {
  final Map<String, double> dataMap;
  final List<Color> colorList;
  final String centerText;
  final double chartRadius;
  final double ringStrokeWidth;

  CommonPieChart({
    required this.dataMap,
    required this.colorList,
    required this.chartRadius,
    required this.ringStrokeWidth,
    this.centerText = '',
  });

  @override
  Widget build() {
    return PieChart(
      dataMap: dataMap,
      colorList: colorList,
      chartType: ChartType.ring,
      chartRadius: chartRadius,
      ringStrokeWidth: ringStrokeWidth,
      centerWidget: Text(
        centerText,
        textAlign: TextAlign.center,
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: chartRadius / 8),
      ),
      legendOptions: const LegendOptions(showLegends: false),
      chartValuesOptions: const ChartValuesOptions(showChartValues: false),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "PieChart",
        "dataMap": dataMap,
        "colorList": colorList.map((c) => '#${c.value.toRadixString(16)}').toList(),
        "centerText": centerText,
        "chartRadius": chartRadius,
        "ringStrokeWidth": ringStrokeWidth,
      };
}

/// Legend widget wrapper composed of other JsonWidgets
class CommonLegend extends JsonWidget {
  final List<String> labels;
  final List<Color> colors;
  final double fontSize;
  
  final JsonWidget _root;

  CommonLegend({
    required this.labels,
    required this.colors,
    this.fontSize = 12,
  }) : _root = _buildLegend(labels, colors, fontSize);

  static JsonWidget _buildLegend(List<String> labels, List<Color> colors, double fontSize) {
    final numRows = (labels.length / 2).ceil();
    
    return CommonColumn(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(numRows, (rowIndex) {
        final int item1Index = rowIndex;
        final int item2Index = rowIndex + numRows;

        return CommonRow(
          children: [
            CommonExpanded(
              child: CommonRow(
                children: [
                 CommonContainer(width: 14, height: 14, color: colors[item1Index]),
                  CommonSizedBox(width: 6),
                  CommonText(labels[item1Index], fontSize: fontSize),
                ],
              ),
            ),
            CommonSizedBox(width: 18),
            CommonExpanded(
              child: item2Index < labels.length
                  ? CommonRow(
                      children: [
                        CommonContainer(width: 14, height: 14, color: colors[item2Index]),
                        CommonSizedBox(width: 6),
                        CommonText(labels[item2Index], fontSize: fontSize),
                      ],
                    )
                  : CommonSizedBox(), // Placeholder for alignment if odd number of items
            ),
          ],
        );
      }),
    );
  }

  @override
  Widget build() => _root.build();

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Legend",
        "fontSize": fontSize,
        "composition": _root.toJson(),
      };
}