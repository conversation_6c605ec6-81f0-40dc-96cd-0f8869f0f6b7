/// Exception thrown when JSON validation fails
class JsonValidationException implements Exception {
  /// The validation error message
  final String message;
  
  /// List of specific validation errors
  final List<String> errors;
  
  /// The JSON that failed validation
  final Map<String, dynamic>? json;
  
  /// The widget type being validated
  final String? widgetType;

  const JsonValidationException(
    this.message, {
    this.errors = const [],
    this.json,
    this.widgetType,
  });

  @override
  String toString() {
    final buffer = StringBuffer('JsonValidationException: $message');
    
    if (widgetType != null) {
      buffer.write(' (Widget type: $widgetType)');
    }
    
    if (errors.isNotEmpty) {
      buffer.write('\nValidation errors:');
      for (final error in errors) {
        buffer.write('\n  - $error');
      }
    }
    
    return buffer.toString();
  }
}
