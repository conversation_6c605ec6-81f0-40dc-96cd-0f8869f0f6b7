import 'package:flutter/material.dart';

/// Interface for widgets that can be serialized to and from JSON
/// 
/// This interface defines the contract that all bidirectional widgets must implement.
/// It provides a standardized way to convert widgets to JSON and create widgets from JSON.
abstract class JsonSerializableWidget {
  /// Converts this widget instance to a JSON map
  /// 
  /// The returned map should contain all the configuration properties
  /// needed to recreate this widget with the same appearance and behavior.
  Map<String, dynamic> to<PERSON>son();

  /// Creates a widget instance from a JSON map
  /// 
  /// This static method should be implemented by each widget class
  /// to create a new instance from the provided JSON configuration.
  /// 
  /// Note: This is typically implemented as a factory constructor
  /// named `from<PERSON><PERSON>` in the actual widget classes.
  // static Widget fromJson(Map<String, dynamic> json);

  /// Gets the widget type identifier
  /// 
  /// This should return a unique string that identifies the widget type.
  /// It's used in the JSON to specify which widget class to instantiate.
  String get widgetType;

  /// Validates the widget's current configuration
  /// 
  /// Returns true if the widget is in a valid state, false otherwise.
  /// This can be used to ensure that the widget can be properly serialized.
  bool validate({List<String>? errors});

  /// Gets a human-readable description of the widget
  /// 
  /// This is useful for debugging and development tools.
  String get description;

  /// Gets the version of the widget's JSON schema
  /// 
  /// This can be used for migration and backward compatibility.
  String get schemaVersion => '1.0.0';

  /// Creates a copy of this widget with modified properties
  /// 
  /// This method should create a new instance of the widget with
  /// the same configuration as this widget, but with the specified
  /// properties modified.
  JsonSerializableWidget copyWith(Map<String, dynamic> changes);

  /// Gets the default configuration for this widget type
  /// 
  /// This returns a JSON map with default values for all properties.
  /// It can be used as a starting point for creating new widgets.
  Map<String, dynamic> getDefaultConfig();

  /// Merges this widget's configuration with another configuration
  /// 
  /// This is useful for applying themes or partial configurations.
  /// The other configuration takes precedence over this widget's configuration.
  Map<String, dynamic> mergeConfig(Map<String, dynamic> other) {
    final thisConfig = toJson();
    final merged = Map<String, dynamic>.from(thisConfig);
    
    other.forEach((key, value) {
      if (value != null) {
        merged[key] = value;
      }
    });
    
    return merged;
  }

  /// Extracts only the changed properties compared to default configuration
  /// 
  /// This returns a minimal JSON map containing only the properties
  /// that differ from the default configuration. This is useful for
  /// creating compact JSON representations.
  Map<String, dynamic> getDelta() {
    final current = toJson();
    final defaults = getDefaultConfig();
    final delta = <String, dynamic>{};
    
    current.forEach((key, value) {
      if (!defaults.containsKey(key) || defaults[key] != value) {
        delta[key] = value;
      }
    });
    
    return delta;
  }
}
