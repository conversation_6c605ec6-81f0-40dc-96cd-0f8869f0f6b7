import 'package:flutter/material.dart';
import 'dart:convert';

import 'package:ui_controls_library/widgets/piechart/common/common_widgets.dart';



/// The main UI builder for the screen
class BorderThicknessWidget extends StatefulWidget {
  final Function(double height) onPressed;
  const BorderThicknessWidget({super.key,required this.onPressed});

  @override
  State<BorderThicknessWidget> createState() => BorderThicknessWidgetState();
}

class BorderThicknessWidgetState extends State<BorderThicknessWidget> {
  late JsonWidget screenWidgetTree;
  String jsonOutput = '';

  final Map<String, double> dataMap = {
    'Point 1': 1, 'Point 2': 1, 'Point 3': 1, 'Point 4': 1,
    'Point 5': 1, 'Point 6': 1, 'Point 7': 1, 'Point 8': 1,
  };

  final List<Color> colorList = [
    Color(0xFF0D47A1), Color(0xFF1565C0), Color(0xFF1976D2), Color(0xFF1E88E5),
    Color(0xFF42A5F5), Color(0xFF64B5F6), Color(0xFF90CAF9), Color(0xFFBBDEFB),
  ];

  JsonWidget buildChartSection({
    required String sectionTitle,
    required double headingFontSize,
    required double bodyFontSize,
    required double labelFontSize,
    required double chartRadius,
    required double cardElevation,
  }) {
    return CommonColumn(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(sectionTitle, fontSize: 24, fontWeight: FontWeight.normal),
        CommonSizedBox(height: 12),
        
        CommonCard(
          elevation: cardElevation,
          borderRadius: 12,
          child: CommonColumn(
            children: [
              CommonText("Temperature theme", fontSize: bodyFontSize, fontWeight: FontWeight.w600),
              CommonSizedBox(height: 8),
              CommonRow(
                children: [
                  CommonExpanded(
                    flex: 5,
                    child: CommonPieChart(
                      dataMap: dataMap,
                      colorList: colorList,
                      chartRadius: chartRadius,
                      ringStrokeWidth: chartRadius * 0.2,
                      centerText: "Total Value\n\$9,999.99",
                    ),
                  ),
                  CommonSizedBox(width: 24),
                  CommonExpanded(
                    flex: 4,
                    child: CommonLegend(
                      labels: dataMap.keys.toList(),
                      colors: colorList,
                      fontSize: labelFontSize,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        CommonSizedBox(height: 24),
        CommonText("Properties", fontWeight: FontWeight.bold, fontSize: bodyFontSize, color: Colors.black,textDecoration: TextDecoration.underline),
        CommonText("Border-width  ${headingFontSize.toInt()}", fontSize: headingFontSize, fontWeight: FontWeight.w500),
      
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    screenWidgetTree = CommonColumn(
      children: [
        CommonText('Properties - Border Thickness', fontSize: 33, fontWeight: FontWeight.bold),
        CommonSizedBox(height: 32),
        CommonRow(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CommonExpanded(
              child: buildChartSection(
                sectionTitle: "Small",
                cardElevation: 0.8,
                headingFontSize: 14, bodyFontSize: 14, labelFontSize: 12, chartRadius: 100,
              ),
            ),
            CommonSizedBox(width: 32),
            CommonExpanded(
              child: buildChartSection(
                sectionTitle: "Medium",
                cardElevation: 0.8,
                headingFontSize: 16, bodyFontSize: 14, labelFontSize: 12, chartRadius: 100,
              ),
            ),
            CommonSizedBox(width: 32),
            CommonExpanded(
              child: buildChartSection(
                sectionTitle: "Large",
                cardElevation: 0.5,
                headingFontSize: 18, bodyFontSize: 14, labelFontSize: 12, chartRadius: 100,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<dynamic> generateJson() async {
    final generated = screenWidgetTree.toJson();
    setState(() {
      jsonOutput = const JsonEncoder.withIndent('  ').convert(generated);
    });
    print("=====================================   ${jsonEncode(generated)}  ===============================");
    return generated;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32),
      child: screenWidgetTree.build(),
    );
  }
}