
import 'dart:developer';

import 'package:builder_app/features/login/presentation/bloc/home/<USER>';
import 'package:builder_app/features/login/presentation/bloc/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_bloc.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_event.dart';
import 'package:go_router/go_router.dart';

import '../login/presentation/widgets/common_button.dart';
import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;



class HomeScreen extends StatefulWidget {
  final String? welcomeMessage;
  final Map<String, dynamic>? userData;
  final int? selectedTab;
  final Map<String, dynamic>? settingsData;

  const HomeScreen({
    super.key,
    this.welcomeMessage,
    this.userData,
    this.selectedTab,
    this.settingsData,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {

    double dropdownvalue = 0.5;

  // List of items in our dropdown menu
  var items = [
      0.5,
      1,
      2,
  ];
  int selectedIndex = 0;

  final typographyKey = GlobalKey<ui_controls.PieChartUIBuilderState>();
  final borderThicknessKey = GlobalKey<ui_controls.BorderThicknessWidgetState>();

  final smallConfig = ui_controls.ChartSizeConfig(
    size: ui_controls.ChartSizeType.small,
    headingFontSize: 14,
    bodyFontSize: 12,
    labelFontSize: 10,
    chartRadius: 80,
    propertyType: 'Properties - Typography'
  );

  final mediumConfig = ui_controls.ChartSizeConfig(
    size: ui_controls.ChartSizeType.medium,
    headingFontSize: 16,
    bodyFontSize: 14,
    labelFontSize: 12,
    chartRadius: 100,
    propertyType: 'Properties - Typography'
  );

  final largeConfig = ui_controls.ChartSizeConfig(
    size: ui_controls.ChartSizeType.large,
    headingFontSize: 18,
    bodyFontSize: 16,
    labelFontSize: 12,
    chartRadius: 110,
    propertyType: 'Properties - Typography'
  );

 late ui_controls.ChartSizeConfig selectedConfig;

  Widget _buildResponsiveBody() {
    switch (selectedIndex) {
      case 0:
        return ui_controls.PieChartUIBuilder(
          key: typographyKey,
          onPressed: (height) {},
          config: selectedConfig,
        );
      case 1:
        return ui_controls.BorderThicknessWidget(
          key: borderThicknessKey,
          onPressed: (height) {},
        );
      case 2:
        return const Center(child: Text("Border Radius UI Coming Soon"));
      default:
        return const Center(child: Text("Select a section"));
    }
  }

  void _onSidebarItemTapped(int index) {
    setState(() {
      selectedIndex = index;
    });
  }
@override
  void initState() {
    selectedConfig=smallConfig;
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveBreakpoints.of(context).isMobile;

    final sidebar = isMobile
        ? _SidebarDrawer(onTap: _onSidebarItemTapped)
        : _SidebarRail(onTap: _onSidebarItemTapped);

    final body = Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: _buildResponsiveBody(),
              ),
              Container(
                width: 150,
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const Text("Chart Size", style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () {
                        setState(() {
                          selectedConfig = smallConfig;
                        });
                      },
                      child: const Text("Small"),
                    ),
                    TextButton(
                      onPressed: () {
                        setState(() {
                          selectedConfig = mediumConfig;
                        });
                      },
                      child: const Text("Medium"),
                    ),
                    TextButton(
                      onPressed: () {
                        setState(() {
                          selectedConfig = largeConfig;
                        });
                      },
                      child: const Text("Large"),
                    ),
                    DropdownButton(
          value: dropdownvalue,
          icon: const Icon(Icons.keyboard_arrow_down),
          items:
              items.map((items) {
                return DropdownMenuItem(value: items, child: Text(items.toString()));
              }).toList(),
          onChanged: (newValue) {
            setState(() {
              dropdownvalue = newValue!.toDouble();
              selectedConfig = selectedConfig.copyWith(borderThikness: dropdownvalue);

            });
          },
        ),
      
                    //  TextButton(
                    //   onPressed: () {
                    //     setState(() {
                    //       // selectedConfig = ui_controls.ChartSizeConfig(borderThikness: 2);
                    //       selectedConfig = selectedConfig.copyWith(borderThikness: 0.5);

                    //     });
                    //   },
                    //   child: const Text("Thickness"),
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: CommonButton(
            text: 'Elevated Action',
            tooltip: 'Perform an action',
            onPressed: () async {
              if (selectedIndex == 0) {
                log("=================");
                final jsonOutput = await typographyKey.currentState?.generateJson();
                if (jsonOutput != null && context.mounted) {
                  log("Serialized PieChartData: $jsonOutput");
                  context.go("/json", extra: jsonOutput);
                }
              }
              if (selectedIndex == 1) {
                final jsonOutput = await borderThicknessKey.currentState?.generateJson();
                log("Serialized Border Thickness: $jsonOutput");
              }
            },
            child: const Text("Next", style: TextStyle(color: Colors.white)),
          ),
        ),
      ],
    );

    return BlocProvider(
      create: (context) => HomeBloc()..add(FetchDropdownA()),
      child: isMobile
          ? Scaffold(
              appBar: AppBar(
                title: Text(widget.welcomeMessage ?? 'Bloc'),
                leading: Builder(
                  builder: (context) => IconButton(
                    icon: const Icon(Icons.menu),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                  ),
                ),
              ),
              drawer: sidebar,
              body: body,
            )
          : Scaffold(
              body: Row(
                children: [
                  SizedBox(width: 48, child: sidebar),
                  const VerticalDivider(width: 0.5),
                  Expanded(child: body),
                ],
              ),
            ),
    );
  }
}


class _SidebarDrawer extends StatelessWidget {
  final void Function(int) onTap;

  const _SidebarDrawer({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          const SizedBox(height: 16),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text("Typography"),
            onTap: () {
              Navigator.pop(context);
              onTap(0);
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text("Border Thickness"),
            onTap: () {
              Navigator.pop(context);
              onTap(1);
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text("Border Radius"),
            onTap: () {
              Navigator.pop(context);
              onTap(2);
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text("Logout"),
            onTap: () {
              Navigator.pop(context);
              context.read<AppConfigBloc>().add(LogoutEvent());
            },
          ),
        ],
      ),
    );
  }
}

class _SidebarRail extends StatelessWidget {
  final void Function(int) onTap;

  const _SidebarRail({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48,
      color: Theme.of(context).colorScheme.primary.withAlpha(30),
      child: Column(
        children: [
          const SizedBox(height: 16),
          IconButton(
            icon: const Icon(Icons.home, size: 20),
            tooltip: 'Typography',
            onPressed: () => onTap(0),
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.person, size: 20),
            tooltip: 'Border Thickness',
            onPressed: () => onTap(1),
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.settings, size: 20),
            tooltip: 'Border Radius',
            onPressed: () => onTap(2),
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.logout, size: 20),
            tooltip: 'Logout',
            onPressed: () {
              context.read<AppConfigBloc>().add(LogoutEvent());
            },
          ),
          const Spacer(),
        ],
      ),
    );
  }
}





