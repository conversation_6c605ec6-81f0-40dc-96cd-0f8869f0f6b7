import 'package:flutter/material.dart';

/// Utility class for widget-related operations
class WidgetUtils {
  /// Converts FontWeight to index for JSON serialization
  static int? fontWeightToIndex(FontWeight? fontWeight) {
    if (fontWeight == null) return null;
    return FontWeight.values.indexOf(fontWeight);
  }

  /// Converts index to FontWeight for JSON deserialization
  static FontWeight? indexToFontWeight(int? index) {
    if (index == null || index < 0 || index >= FontWeight.values.length) {
      return null;
    }
    return FontWeight.values[index];
  }

  /// Converts TextAlign to string for JSON serialization
  static String? textAlignToString(TextAlign? textAlign) {
    return textAlign?.name;
  }

  /// Converts string to TextAlign for JSON deserialization
  static TextAlign? stringToTextAlign(String? textAlign) {
    if (textAlign == null) return null;
    
    switch (textAlign.toLowerCase()) {
      case 'left':
        return TextAlign.left;
      case 'right':
        return TextAlign.right;
      case 'center':
        return TextAlign.center;
      case 'justify':
        return TextAlign.justify;
      case 'start':
        return TextAlign.start;
      case 'end':
        return TextAlign.end;
      default:
        return null;
    }
  }

  /// Converts MainAxisAlignment to string for JSON serialization
  static String? mainAxisAlignmentToString(MainAxisAlignment? alignment) {
    return alignment?.name;
  }

  /// Converts string to MainAxisAlignment for JSON deserialization
  static MainAxisAlignment? stringToMainAxisAlignment(String? alignment) {
    if (alignment == null) return null;
    
    switch (alignment.toLowerCase()) {
      case 'start':
        return MainAxisAlignment.start;
      case 'end':
        return MainAxisAlignment.end;
      case 'center':
        return MainAxisAlignment.center;
      case 'spaceBetween':
      case 'space_between':
        return MainAxisAlignment.spaceBetween;
      case 'spaceAround':
      case 'space_around':
        return MainAxisAlignment.spaceAround;
      case 'spaceEvenly':
      case 'space_evenly':
        return MainAxisAlignment.spaceEvenly;
      default:
        return null;
    }
  }

  /// Converts CrossAxisAlignment to string for JSON serialization
  static String? crossAxisAlignmentToString(CrossAxisAlignment? alignment) {
    return alignment?.name;
  }

  /// Converts string to CrossAxisAlignment for JSON deserialization
  static CrossAxisAlignment? stringToCrossAxisAlignment(String? alignment) {
    if (alignment == null) return null;
    
    switch (alignment.toLowerCase()) {
      case 'start':
        return CrossAxisAlignment.start;
      case 'end':
        return CrossAxisAlignment.end;
      case 'center':
        return CrossAxisAlignment.center;
      case 'stretch':
        return CrossAxisAlignment.stretch;
      case 'baseline':
        return CrossAxisAlignment.baseline;
      default:
        return null;
    }
  }

  /// Converts EdgeInsets to JSON map
  static Map<String, double>? edgeInsetsToJson(EdgeInsets? insets) {
    if (insets == null) return null;
    
    return {
      'left': insets.left,
      'top': insets.top,
      'right': insets.right,
      'bottom': insets.bottom,
    };
  }

  /// Converts JSON map to EdgeInsets
  static EdgeInsets? jsonToEdgeInsets(Map<String, dynamic>? json) {
    if (json == null) return null;
    
    return EdgeInsets.only(
      left: (json['left'] as num?)?.toDouble() ?? 0.0,
      top: (json['top'] as num?)?.toDouble() ?? 0.0,
      right: (json['right'] as num?)?.toDouble() ?? 0.0,
      bottom: (json['bottom'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// Converts BorderRadius to JSON map
  static Map<String, dynamic>? borderRadiusToJson(BorderRadius? borderRadius) {
    if (borderRadius == null) return null;
    
    // Handle circular border radius
    if (borderRadius is BorderRadius) {
      final topLeft = borderRadius.topLeft;
      final topRight = borderRadius.topRight;
      final bottomLeft = borderRadius.bottomLeft;
      final bottomRight = borderRadius.bottomRight;
      
      // Check if all corners are the same (circular)
      if (topLeft == topRight && topRight == bottomLeft && bottomLeft == bottomRight) {
        return {
          'type': 'circular',
          'radius': topLeft.x,
        };
      }
      
      // Different corners
      return {
        'type': 'custom',
        'topLeft': {'x': topLeft.x, 'y': topLeft.y},
        'topRight': {'x': topRight.x, 'y': topRight.y},
        'bottomLeft': {'x': bottomLeft.x, 'y': bottomLeft.y},
        'bottomRight': {'x': bottomRight.x, 'y': bottomRight.y},
      };
    }
    
    return null;
  }

  /// Converts JSON map to BorderRadius
  static BorderRadius? jsonToBorderRadius(Map<String, dynamic>? json) {
    if (json == null) return null;
    
    final type = json['type'] as String?;
    
    if (type == 'circular') {
      final radius = (json['radius'] as num?)?.toDouble() ?? 0.0;
      return BorderRadius.circular(radius);
    }
    
    if (type == 'custom') {
      final topLeft = json['topLeft'] as Map<String, dynamic>?;
      final topRight = json['topRight'] as Map<String, dynamic>?;
      final bottomLeft = json['bottomLeft'] as Map<String, dynamic>?;
      final bottomRight = json['bottomRight'] as Map<String, dynamic>?;
      
      return BorderRadius.only(
        topLeft: Radius.elliptical(
          (topLeft?['x'] as num?)?.toDouble() ?? 0.0,
          (topLeft?['y'] as num?)?.toDouble() ?? 0.0,
        ),
        topRight: Radius.elliptical(
          (topRight?['x'] as num?)?.toDouble() ?? 0.0,
          (topRight?['y'] as num?)?.toDouble() ?? 0.0,
        ),
        bottomLeft: Radius.elliptical(
          (bottomLeft?['x'] as num?)?.toDouble() ?? 0.0,
          (bottomLeft?['y'] as num?)?.toDouble() ?? 0.0,
        ),
        bottomRight: Radius.elliptical(
          (bottomRight?['x'] as num?)?.toDouble() ?? 0.0,
          (bottomRight?['y'] as num?)?.toDouble() ?? 0.0,
        ),
      );
    }
    
    return null;
  }

  /// Converts BoxShadow to JSON map
  static Map<String, dynamic>? boxShadowToJson(BoxShadow? shadow) {
    if (shadow == null) return null;
    
    return {
      'color': shadow.color.value,
      'offset': {'dx': shadow.offset.dx, 'dy': shadow.offset.dy},
      'blurRadius': shadow.blurRadius,
      'spreadRadius': shadow.spreadRadius,
    };
  }

  /// Converts JSON map to BoxShadow
  static BoxShadow? jsonToBoxShadow(Map<String, dynamic>? json) {
    if (json == null) return null;
    
    final colorValue = json['color'] as int?;
    final offset = json['offset'] as Map<String, dynamic>?;
    
    return BoxShadow(
      color: colorValue != null ? Color(colorValue) : Colors.black,
      offset: Offset(
        (offset?['dx'] as num?)?.toDouble() ?? 0.0,
        (offset?['dy'] as num?)?.toDouble() ?? 0.0,
      ),
      blurRadius: (json['blurRadius'] as num?)?.toDouble() ?? 0.0,
      spreadRadius: (json['spreadRadius'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// Converts a list of BoxShadow to JSON array
  static List<Map<String, dynamic>>? boxShadowListToJson(List<BoxShadow>? shadows) {
    if (shadows == null || shadows.isEmpty) return null;
    
    return shadows.map((shadow) => boxShadowToJson(shadow)!).toList();
  }

  /// Converts JSON array to list of BoxShadow
  static List<BoxShadow>? jsonToBoxShadowList(List<dynamic>? json) {
    if (json == null || json.isEmpty) return null;
    
    return json
        .map((item) => jsonToBoxShadow(item as Map<String, dynamic>))
        .where((shadow) => shadow != null)
        .cast<BoxShadow>()
        .toList();
  }

  /// Gets a human-readable description of a widget type
  static String getWidgetDescription(String widgetType) {
    switch (widgetType.toLowerCase()) {
      case 'calendar':
        return 'A customizable calendar widget with date selection and event support';
      case 'button':
        return 'A clickable button widget with customizable appearance';
      case 'text':
        return 'A text display widget with formatting options';
      case 'container':
        return 'A container widget for layout and decoration';
      case 'column':
        return 'A vertical layout widget that arranges children in a column';
      case 'row':
        return 'A horizontal layout widget that arranges children in a row';
      default:
        return 'A custom widget';
    }
  }

  /// Validates widget configuration
  static bool validateWidgetConfig(Map<String, dynamic> config, {List<String>? errors}) {
    errors?.clear();
    bool isValid = true;

    // Check for required widgetType field
    if (!config.containsKey('widgetType')) {
      errors?.add('Missing required field: widgetType');
      isValid = false;
    }

    // Validate numeric fields are non-negative
    final numericFields = ['width', 'height', 'fontSize', 'borderWidth', 'borderRadius'];
    for (final field in numericFields) {
      if (config.containsKey(field) && config[field] != null) {
        final value = config[field];
        if (value is num && value < 0) {
          errors?.add('$field must be non-negative');
          isValid = false;
        }
      }
    }

    return isValid;
  }
}
